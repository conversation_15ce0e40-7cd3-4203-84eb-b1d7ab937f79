services:
  freqtrade:
    image: freqtradeorg/freqtrade:stable
    restart: unless-stopped
    container_name: freqtrade
    volumes:
      - "/home/<USER>/freqtrade/user_data:/freqtrade/user_data"
    ports:
      - "8080:8080" # 用于服务器的API和webUI访问
    command: >
      trade
      --user-data-dir /freqtrade/user_data
      --config /freqtrade/user_data/live.json
      --logfile /freqtrade/user_data/logs/freqtrade.log
    environment:
      - TZ=Asia/Shanghai
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
