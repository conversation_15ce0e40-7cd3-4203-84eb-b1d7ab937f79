services:
  freqtrade:
    image: freqtradeorg/freqtrade:stable
    container_name: freqtrade
    restart: unless-stopped
    volumes:
      - "./user_data:/freqtrade/user_data"
      - /Users/<USER>/Library/CloudStorage/OneDrive-个人/开发/freqtrade/user_data/binance_leverage_tiers.json:/freqtrade/freqtrade/exchange/binance_leverage_tiers.json
    ports:
      - "8080:8080"   # 用于本地的API和webUI访问
    command: >
      webserver
      --user-data-dir /freqtrade/user_data
      --config /freqtrade/user_data/backtest.json
      --logfile /freqtrade/user_data/logs/freqtrade.log
    environment:
      - TZ=Asia/Shanghai
    networks:
      - ft_network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  ft_network:
    name: ft_network
    driver: bridge