{
    "stake_currency": "USDT",
    "fiat_display_currency": "USD",
    "dry_run": true,
    "dry_run_wallet": 10000, /* 模拟交易钱包初始金额 */
    "available_capital": 10000,
    "tradable_balance_ratio": 0.99,
    "max_open_trades": 50,
    "stake_amount": "unlimited",
    "amend_last_stake_amount": false,
    "last_stake_amount_min_ratio": 0.5,
    "position_adjustment_enable": true,
    "process_only_new_candles": true,
    "cancel_open_orders_on_exit": false,
    "ignore_roi_if_entry_signal": true,
    "trading_mode": "futures", /* 交易模式：期货 */
    "margin_mode": "cross", /* 保证金模式：逐仓是isolated，全仓是cross */
    "futures_funding_rate": true,
    "validate_leverage_tiers": false,
    "timeframe": "1h",
    "timezone": "Asia/Shanghai",
    "user_data_dir": "/freqtrade/user_data",
    "db_url": "sqlite:////freqtrade/user_data/tradesv3.sqlite",
    "log": {
        "level": "DEBUG",
        "file": "/freqtrade/user_data/logs/freqtrade.log",
        "rotation": {
            "max_size": "10MB",
            "retention": "7 days"
        }
    },
    "unfilledtimeout": {
        "entry": 10,
        "exit": 10,
        "exit_timeout_count": 0,
        "unit": "minutes"
    },
    "order_types": {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": true,
        "stoploss_on_exchange_interval": 60
    },
    "entry_pricing": {
        "price_side": "other", /* 入场价格方向，other表示使用对手方价格 */
        "use_order_book": true, /* 是否使用订单簿 */
        "order_book_top": 1, /* 使用订单簿的深度 */
        "price_last_balance": 0.0,
        "check_depth_of_market": {
            "enabled": false
        }
    },
    "exit_pricing": {
        "price_side": "other",
        "use_order_book": true,
        "order_book_top": 1
    },
    "exchange": {
        "name": "binance",
        "key": "",
        "secret": "",
        "ccxt_async_config": {
            "enableRateLimit": true,
            "defaultType": "future",
            "rateLimit": 50
        },
        "ccxt_config": {
            "enableRateLimit": true,
            "defaultType": "future",
            "rateLimit": 50
        },
        "exchange_info_pairs_refresh": true,
        "pair_whitelist": [".*USDT:USDT"],
        "pair_blacklist": [
            "USDC/USDT:USDT",
            "BTCDOM/USDT:USDT"
        ]
    },
    "pairlists": [
        {
            "method": "StaticPairList"
        },
        {
            "method": "PerformanceFilter",
            "minutes": 4320,
            "min_profit": 0.50
        }
    ],
    "api_server": {
        "enabled": true,
        "listen_ip_address": "0.0.0.0",
        "listen_port": 8080,
        "verbosity": "info",
        "enable_openapi": true,
        "jwt_secret_key": "7xL9#kQ2pZ4!vR8&wE5@tY6*uI1(oP3",
        "ws_token": "sdrfxvbcx34567",
        "CORS_origins": [
            "*"
        ],
        "username": "freqtrade",
        "password": "freqtrade123"
    },
    "bot_name": "freqtrade",
    "initial_state": "running",
    "force_entry_enable": false,
    "internals": {
        "process_throttle_secs": 3,
        "heartbeat_interval": 60,
        "sd_notify": true
    },
    "telegram": {
        "enabled": false,
        "token": "7861280994:AAFZqV1IGFFEb2oMVNvt70oFULmpn1S9rGU",
        "chat_id": "432493748"
    }
}