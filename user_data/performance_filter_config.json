{"stake_currency": "USDT", "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 10000, "available_capital": 10000, "tradable_balance_ratio": 0.99, "max_open_trades": 50, "stake_amount": "unlimited", "amend_last_stake_amount": false, "last_stake_amount_min_ratio": 0.5, "position_adjustment_enable": true, "process_only_new_candles": true, "cancel_open_orders_on_exit": false, "ignore_roi_if_entry_signal": true, "trading_mode": "futures", "margin_mode": "cross", "futures_funding_rate": true, "validate_leverage_tiers": false, "timeframe": "1h", "timezone": "Asia/Shanghai", "user_data_dir": "/freqtrade/user_data", "db_url": "sqlite:////freqtrade/user_data/tradesv3.sqlite", "log": {"level": "INFO", "file": "/freqtrade/user_data/logs/freqtrade.log", "rotation": {"max_size": "10MB", "retention": "7 days"}}, "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "order_types": {"entry": "market", "exit": "market", "emergency_exit": "market", "force_entry": "market", "force_exit": "market", "stoploss": "market", "stoploss_on_exchange": true, "stoploss_on_exchange_interval": 60}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "your_api_key_here", "secret": "your_api_secret_here", "enable_ws": true, "_ft_has_params": {"ohlcv_candle_limit": 50}, "ccxt_config": {"enableRateLimit": true, "defaultType": "future", "rateLimit": 50}, "pair_whitelist": [".*USDT:USDT"], "pair_blacklist": ["USDC/USDT:USDT", "BTCDOM/USDT:USDT"]}, "pairlists": [{"method": "VolumePairList", "number_assets": 200, "sort_key": "quoteVolume", "min_value": 1000000, "refresh_period": 18000}, {"method": "PerformanceFilter", "minutes": 4320, "min_profit": 0.5}, {"method": "Sp<PERSON><PERSON><PERSON>er", "max_spread_ratio": 0.005}], "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "your_jwt_secret_here", "ws_token": "your_ws_token_here", "CORS_origins": ["*"]}, "bot_name": "freqtrade_performance_filter", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}