# PerformanceFilter 使用指南

## 概述

本指南说明如何在freqtrade中使用PerformanceFilter来只交易过去某段时间表现良好的交易对，同时保持策略中全市场指标计算的准确性。

## 问题背景

当策略需要计算全市场指标（如平均值、排名等）时，如果直接使用PerformanceFilter，会导致：
1. 交易对列表动态变化
2. 市场指标计算基础数据不稳定
3. 指标计算结果可能出现偏差

## 解决方案

### 1. 策略层面的改进

我们在策略中添加了以下功能：

#### 缓存机制
```python
self._full_market_pairs = None      # 缓存完整的市场交易对列表
self._market_pairs_cache_time = None  # 缓存时间戳
```

#### 专门的市场交易对获取方法
```python
def _get_full_market_pairs(self) -> list:
    """获取完整的市场交易对列表，用于市场指标计算"""
```

这个方法会：
- 缓存完整的市场交易对列表（1小时有效期）
- 获取交易所所有活跃的USDT永续合约
- 限制数量为500个最活跃的交易对
- 在获取失败时回退到当前白名单

#### 市场指标计算优化
市场指标计算现在使用完整的交易对列表，而不是经过PerformanceFilter过滤后的列表。

### 2. 配置文件设置

#### PerformanceFilter配置
```json
{
    "method": "PerformanceFilter",
    "minutes": 4320,        // 3天 = 3 * 24 * 60 = 4320分钟
    "min_profit": 0.50      // 最低50%收益率
}
```

#### 完整的pairlists配置示例
```json
"pairlists": [
    {
        "method": "VolumePairList",
        "number_assets": 200,
        "sort_key": "quoteVolume",
        "min_value": 1000000,
        "refresh_period": 18000
    },
    {
        "method": "PerformanceFilter",
        "minutes": 4320,        // 过去3天
        "min_profit": 0.50      // 最低50%收益
    },
    {
        "method": "PriceFilter",
        "low_price_ratio": 0.01
    },
    {
        "method": "SpreadFilter",
        "max_spread_ratio": 0.005
    }
]
```

## 参数说明

### PerformanceFilter参数

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `minutes` | 回看时间窗口（分钟） | 4320（3天） |
| `min_profit` | 最低收益率要求（比例） | 0.50（50%） |

### 时间窗口计算

- 1天 = 1440分钟
- 3天 = 4320分钟
- 7天 = 10080分钟
- 30天 = 43200分钟

### 收益率设置

- 0.01 = 1%
- 0.10 = 10%
- 0.50 = 50%
- 1.00 = 100%

## 使用步骤

### 1. 更新策略文件
确保你的策略文件包含了新的市场交易对获取方法。

### 2. 配置文件设置
使用提供的配置文件模板 `performance_filter_config.json`。

### 3. 启动机器人
```bash
freqtrade trade --config user_data/performance_filter_config.json --strategy Long_1H_Dev
```

### 4. 监控日志
观察日志中的以下信息：
- "获取到XXX个完整市场交易对用于指标计算"
- "使用完整交易对列表计算市场指标"

## 注意事项

### 1. 启动期要求
- PerformanceFilter需要历史交易数据才能工作
- 建议在机器人有几百笔交易记录后再启用
- 新机器人可能需要运行一段时间才能看到效果

### 2. 性能考虑
- 完整市场交易对列表限制为500个
- 缓存机制减少API调用频率
- 可根据需要调整交易对数量限制

### 3. 回测限制
- PerformanceFilter不支持回测模式
- 只在实盘或模拟交易中有效

### 4. 风险提示
- 过高的min_profit设置可能导致没有符合条件的交易对
- 建议从较低的收益率要求开始，逐步调整

## 监控和调试

### 查看当前交易对列表
```bash
freqtrade test-pairlist --config user_data/performance_filter_config.json
```

### 查看交易表现
```bash
freqtrade show_trades --config user_data/performance_filter_config.json
```

### 日志级别调整
在配置文件中设置：
```json
"log": {
    "level": "DEBUG"  // 获取更详细的日志信息
}
```

## 常见问题

### Q: 为什么没有交易对符合条件？
A: 可能的原因：
- min_profit设置过高
- 历史交易数据不足
- 时间窗口设置不合理

### Q: 市场指标计算是否准确？
A: 是的，市场指标现在基于完整的市场交易对列表计算，不受PerformanceFilter影响。

### Q: 如何调整收益率要求？
A: 修改配置文件中的min_profit值，建议从0.10（10%）开始逐步调整。

## 总结

通过这个解决方案，你可以：
1. 只交易过去3天赚了50%以上的交易对
2. 保持市场指标计算的准确性和稳定性
3. 享受PerformanceFilter带来的风险控制优势

这种方法既满足了性能筛选的需求，又确保了策略指标计算的可靠性。
