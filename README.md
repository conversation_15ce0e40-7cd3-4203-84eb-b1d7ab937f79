# 最小化回测策略文档

## 项目概述

本项目基于Freqtrade框架开发了一个名为All_1H_Up_Dev的交易策略，主要针对1小时K线数据进行交易决策。

## 功能特性

### 核心功能
- 基于1小时K线的趋势跟踪策略
- 支持多交易对并行交易
- 提供详细的交易信号输出

### 性能优化

#### 缓存机制
- 使用`self._last_calculated_date`存储最近计算日期
- 使用`self._market_data_by_time`存储按时间分组后的市场数据
- 使用`self._all_pairs_data`存储所有交易对数据

#### 增量计算
- 通过比较当前日期和`self._last_calculated_date`判断是否需要重新计算
- 仅处理新数据点，跳过已计算的历史数据

#### 向量化操作
- 使用`pandas.DataFrame.rolling`进行滚动计算
- 使用`pandas.DataFrame.pct_change`计算百分比变化
- 使用`pandas.DataFrame.assign`批量添加新列

#### 日志增强
- 记录计算开始和结束时间
- 记录处理的数据点数量
- 记录每个交易对的关键指标

#### 错误处理
- 使用`pandas.DataFrame.columns.contains`检查列是否存在
- 使用`try-except`捕获数据处理异常

## 动态仓位管理

### 基于账户余额的动态下单

策略实现了基于当前账户实际余额的动态仓位管理。这种方式可以更好地适应账户资金的变化，实现更灵活的资金管理。

#### 实现方式

1. **初始化设置**
```python
def __init__(self, config: dict) -> None:
    super().__init__(config)
    # 设置初始值，用于回测初始化
    self.total_stake = config.get('dry_run_wallet', 1000)
```

2. **动态仓位计算**
```python
def custom_stake_amount(self, pair: str, current_time: datetime, 
                       current_rate: float, proposed_stake: float,
                       min_stake: float, max_stake: float,
                       leverage: float, entry_tag: str, **kwargs) -> float:
    # 获取当前USDT余额作为仓位基数
    if hasattr(self, 'wallets') and self.wallets is not None:
        self.total_stake = self.wallets.get_total('USDT')
```

#### 关键特性

- **动态余额更新**：每次下单时自动获取最新账户余额
- **适配多种模式**：同时支持回测、模拟交易和实盘交易
- **安全性保障**：包含错误处理机制，确保策略稳定运行

#### 仓位比例设置

可以通过修改 `BUY_1H_UP_RATIOS` 字典来调整不同入场信号的仓位比例：

```python
self.BUY_1H_UP_RATIOS = {
    'BUY_RISE_1': 0.0325,   # 使用账户总额的6.25%
    'BUY_RISE_2': 0.125,    # 使用账户总额的12.5%
    'BUY_RISE_3': 0.25,     # 使用账户总额的25%
    'BUY_RISE_4': 0.5,      # 使用账户总额的31.25%
    'BUY_MINUTE_4UP': 0.0625 # 使用账户总额的12.5%
}
```

#### 使用注意事项

1. **回测配置**
   - 在 `backtest.json` 中设置 `dry_run_wallet` 作为初始资金
   ```json
   {
       "dry_run_wallet": 1000,
       "stake_currency": "USDT"
   }
   ```

2. **实盘/模拟交易**
   - 策略会自动获取实际USDT余额
   - 确保交易所API权限正确设置

3. **风险管理**
   - 仓位比例应根据实际风险承受能力设置
   - 建议定期检查和调整仓位比例设置

#### 优势

- **资金利用率优化**：根据实际可用资金动态调整仓位
- **风险控制**：通过百分比配置，避免固定金额带来的风险
- **灵活性**：可以根据不同信号设置不同的仓位比例

#### 调试建议

- 使用日志记录监控资金变化：
```python
logger.info(f"当前账户USDT总额: {self.total_stake}")
logger.info(f"本次下单金额: {stake_amount}")
```

- 在回测中验证仓位计算是否符合预期
- 在模拟交易中测试实际资金变化情况

通过这种动态仓位管理方式，策略可以更好地适应市场变化和账户状态，提供更灵活和安全的交易体验。

## 使用说明

### 参数配置
- `--strategy`：指定策略名称（默认：All_1H_Up_Dev）
- `--timeframe`：指定K线周期（默认：1h）
- `--timerange`：指定时间范围，格式为YYYYMMDD-YYYYMMDD
- `--analysis-csv-path`：指定CSV文件输出路径

## 路径配置注意事项

在修改Docker挂载路径时，需要特别注意以下几点：

1. **路径一致性**：
   - 确保 `docker-compose.yml` 中的挂载路径与配置文件中的路径一致
   - 例如：
     ```yaml
     volumes:
       - "./user_data:/freqtrade/ft_userdata/user_data"
     ```
     对应的配置文件路径应为：
     ```json
     "user_data_dir": "/freqtrade/ft_userdata/user_data",
     "db_url": "sqlite:////freqtrade/ft_userdata/user_data/tradesv3.sqlite",
     "log": {
         "file": "/freqtrade/ft_userdata/user_data/logs/freqtrade.log"
     }
     ```

2. **修改步骤**：
   - 修改 `docker-compose.yml` 文件
   - 同步更新配置文件中的相关路径
   - 重启容器使更改生效：
     ```bash
     docker-compose down && docker-compose up -d
     ```

3. **常见问题**：
   - 路径不一致会导致：
     - CPU使用率飙升
     - 文件访问失败
     - 系统不稳定
   - 解决方法：
     - 检查并统一所有路径
     - 查看容器日志定位问题
     - 确保文件权限正确

4. **最佳实践**：
   - 使用相对路径（如 `./user_data`）以便于迁移
   - 在修改前备份配置文件
   - 修改后立即检查容器状态和日志

## Freqtrade不同启动方式指南

### 1. 回测模式（Backtesting）

回测模式用于在历史数据上测试策略性能，不会执行实际交易。

#### 使用docker-compose启动回测服务器

```bash
# 启动回测Web界面服务器
docker-compose -f docker-compose.backtest.yml up -d
```

回测Web界面可通过 http://localhost:8080 访问。

#### 命令行回测

```bash
# 基本回测命令
docker exec freqtrade freqtrade backtesting \
  --strategy All_1H_Up_Dev \
  --strategy-path /freqtrade/ft_userdata/user_data/strategies \
  --timerange 20240101-20240103 \
  --config /freqtrade/ft_userdata/user_data/backtest.json \
  --export signals

# 使用更详细的时间粒度进行回测
docker exec freqtrade freqtrade backtesting \
  --strategy All_1H_Up_Dev \
  --timeframe 1h \
  --timeframe-detail 1m \
  --timerange 20240101-20240103 \
  --config /freqtrade/ft_userdata/user_data/backtest.json \
  --export signals
```

#### 回测注意事项

- **数据下载**：首次回测前需要下载历史数据
  ```bash
  docker exec freqtrade freqtrade download-data \
    --timerange 20240101-20240103 \
    --timeframe 1h \
    --config /freqtrade/ft_userdata/user_data/backtest.json
  ```

- **配置文件路径**：确保`user_data_dir`路径正确
  ```json
  "user_data_dir": "/freqtrade/ft_userdata/user_data"
  ```

- **策略路径**：使用`--strategy-path`参数指定策略文件位置

### 2. 模拟交易模式（Dry-run）

模拟交易模式会实时运行策略，但不执行实际交易，适合在部署到实盘前测试。

#### 使用docker-compose启动模拟交易

```bash
# 启动模拟交易
docker-compose -f docker-compose.dryrun.yml up -d
```

#### 模拟交易配置示例

```yaml
command: >
  trade
  --logfile /freqtrade/ft_userdata/user_data/logs/freqtrade.log
  --config /freqtrade/ft_userdata/user_data/backtest.json
  --strategy All_1H_Up_Dev
  --db-url sqlite:////freqtrade/ft_userdata/user_data/database/tradesv3.sqlite
  --user-data-dir /freqtrade/ft_userdata/user_data
```

## 在策略中使用不同时间周期数据

在Freqtrade策略中，有多种方式可以使用不同时间周期的数据（如在1小时策略中使用1分钟数据）。以下是几种主要方法的比较和最佳实践。

### 1. 使用informative_pairs方法

```python
def informative_pairs(self):
    pairs = self.dp.current_whitelist()
    informative_pairs = []
    for pair in pairs:
        informative_pairs.append((pair, self.timeframe))  # 主时间周期
        informative_pairs.append((pair, '1m'))  # 额外时间周期
    return informative_pairs
```

**优点**：
- 所有数据预先下载和缓存，访问速度快
- 适合需要大量使用小时间周期数据的策略

**缺点**：
- 下载所有交易对的数据，即使大部分不会用到
- 增加内存和网络带宽使用

### 2. 使用DataProvider按需获取数据

```python
def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float, 
                      time_in_force: str, current_time: datetime, **kwargs) -> bool:
    # 只在需要时获取1分钟数据
    minute_df = self.dp.get_pair_dataframe(pair=pair, timeframe='1m')
    # 进行分析...
    return True  # 或 False
```

**优点**：
- 按需获取数据，节省资源
- 只为满足条件的交易对获取数据
- 更加灵活，可在任何需要的地方使用

**缺点**：
- 首次获取数据可能有短暂延迟
- 需要处理可能的数据获取失败情况

### 3. 使用@informative装饰器

```python
@informative('1m')
def populate_indicators_1m(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
    # 计算1分钟指标
    dataframe['minute_close_open_diff'] = dataframe['close'] - dataframe['open']
    return dataframe
```

**优点**：
- 代码结构清晰
- 自动处理数据获取和处理

**缺点**：
- 默认情况下不会将小时间周期数据合并到大时间周期
- 可能需要额外的数据合并步骤

### 最佳实践推荐

对于"第N个1分钟上涨时入场/加仓"等需求，推荐使用**DataProvider按需获取数据**的方式：

1. **入场确认**：在`confirm_trade_entry`方法中实现
   ```python
   def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float, 
                         time_in_force: str, current_time: datetime, **kwargs) -> bool:
       minute_df = self.dp.get_pair_dataframe(pair=pair, timeframe='1m')
       # 检查是否是第3个连续上涨的1分钟K线
       # ...
       return is_third_up_minute  # True或False
   ```

2. **加仓确认**：在`adjust_trade_position`方法中实现
   ```python
   def adjust_trade_position(self, trade: Trade, current_time: datetime, **kwargs):
       minute_df = self.dp.get_pair_dataframe(pair=trade.pair, timeframe='1m')
       # 检查是否是第5个连续上涨的1分钟K线
       # ...
       return stake_amount, "1分钟连续第5次上涨加仓"  # 或 None表示不加仓
   ```

这种方法既高效又灵活，特别适合只在特定条件下需要使用小时间周期数据的策略。

#### 模拟交易注意事项

- **数据库路径**：确保数据库路径正确，避免数据丢失
- **日志文件**：设置正确的日志路径便于排查问题
- **策略参数**：可以在命令行或配置文件中设置策略参数

### 3. 实盘交易模式（Live Trading）

实盘交易模式会执行真实的交易操作，请谨慎使用。

#### 使用docker-compose启动实盘交易

```bash
# 启动实盘交易
docker-compose -f docker-compose.yml up -d
```

#### 实盘交易配置示例

```yaml
command: >
  trade
  --logfile /freqtrade/ft_userdata/user_data/logs/freqtrade.log
  --config /freqtrade/ft_userdata/user_data/config.json
  --strategy All_1H_Up_Dev
```

#### 实盘交易注意事项

- **API密钥**：确保交易所API密钥正确配置
- **风险控制**：设置合理的仓位大小和止损参数
- **监控系统**：建议配置监控和告警系统

### 4. Web界面模式

提供图形化界面进行回测、查看交易历史和管理策略。

#### 使用docker-compose启动Web界面

```bash
# 启动Web界面
docker-compose -f docker-compose.backtest.yml up -d
```

#### Web界面配置示例

```yaml
command: >
  webserver
  --logfile /freqtrade/ft_userdata/user_data/logs/freqtrade.log
  --config /freqtrade/ft_userdata/user_data/backtest.json
  --user-data-dir /freqtrade/ft_userdata/user_data
```

#### Web界面功能

- **回测**：通过界面配置和运行回测
- **性能分析**：查看策略性能指标和图表
- **交易历史**：查看历史交易记录
- **参数优化**：进行超参数优化

### 5. 常见问题与解决方案

#### 策略加载失败

**问题**：`Impossible to load Strategy 'XXX'. This class does not exist or contains Python code errors.`

**解决方案**：
- 检查策略文件路径是否正确
- 使用`--strategy-path`参数指定策略位置
- 检查策略代码是否有语法错误

#### 数据路径问题

**问题**：`No data found. Terminating.`

**解决方案**：
- 确保已下载所需的历史数据
- 检查`user_data_dir`配置是否正确
- 检查数据目录权限

#### 跨交易对指标计算

**最佳实践**：
- 使用`informative_pairs`方法声明需要的交易对
- 将市场指标计算逻辑分离到独立方法中
- 使用策略实例变量存储计算结果避免重复计算

```python
def informative_pairs(self):
    # 获取当前白名单中的所有交易对
    pairs = self.dp.current_whitelist()
    # 为每个交易对分配时间框架
    informative_pairs = [(pair, self.timeframe) for pair in pairs]
    return informative_pairs
```

## 接口说明
### 关键配置参数
```python
class minimize_backtest(IStrategy):
    signal_validity = 600  # 信号有效期（秒）
    max_db_errors = 3      # 数据库最大错误次数
    stoploss = -0.05       # 止损比例
    timeframe = '5m'       # K线周期
```

### 数据库依赖
- 必需环境变量：
  ```bash
  DB_URL=postgresql://<user>:<password>@<host>/<database>
  ```
- 数据表结构要求：
  ```sql
  CREATE TABLE trend_signals (
    generation_time BIGINT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL
  );
  ```

## 指标分析
### 查看和导出指标数据
需要先回测加入信号导出--export signals ：
docker exec freqtrade freqtrade backtesting --strategy All_1H_Up_Dev --timeframe 1h --timeframe-detail 1m --timerange 20240101-20240103 --config /freqtrade/ft_userdata/user_data/config.json --export signals 

使用以下命令可以查看指标数据并导出为CSV文件：

```bash
# 查看指标数据并导出为CSV
docker compose run freqtrade backtesting-analysis \
    --config /freqtrade/ft_userdata/user_data/config.json \
    --analysis-groups 1 \
    --indicator-list consecutive_count last_1h_increase increase_3h increase_4h increase_6h increase_12h \
                     k_increase k1_increase k2_increase k3_increase volume_usd_24h \
                     last_1h_amplitude last_1h_drawdown k00_up k01_up up_k_count \
    --analysis-to-csv
```

此命令会生成两个CSV文件：
1. `user_data/backtest_results/group_1.csv` - 包含第一组分析结果
2. `user_data/backtest_results/indicators.csv` - 包含所有指标的分析结果

### 主要指标说明
- `consecutive_count`：连续上涨次数
- `last_1h_increase`：最近1小时涨幅
- `increase_3h/4h/6h/12h`：3/4/6/12小时涨幅
- `k_increase, k1_increase, k2_increase, k3_increase`：当前及前几根K线涨幅
- `volume_usd_24h`：24小时交易量
- `last_1h_amplitude, last_1h_drawdown`：最近1小时振幅和回撤
- `k00_up, k01_up`：00分钟和01分钟K线涨跌
- `up_k_count`：每小时上涨K线数量

### 可选参数
- `--timerange`：指定时间范围，格式为YYYYMMDD-YYYYMMDD
- `--analysis-csv-path`：指定CSV文件输出路径

## 性能优化

- **缓存机制**：在`_calculate_market_indicators`函数中实现了缓存，存储了最近计算日期、按时间分组后的市场数据以及所有交易对数据，避免了重复计算。

- **增量计算**：引入增量计算逻辑，仅处理新数据点，减少了对历史数据的重复计算。

- **数据范围过滤**：通过`max_history_points`参数限制处理的历史数据量，优化了性能。

- **向量化操作**：使用pandas的向量化操作（如`rolling`、`pct_change`）提高计算效率。

- **日志增强**：添加了详细的日志记录，跟踪计算过程、处理的数据点数量以及关键指标。

- **错误处理**：增加了对DataFrame列存在性的检查，提高了代码的健壮性。

## 技术实现细节

#### 缓存机制
- 使用`self._last_calculated_date`存储最近计算日期
- 使用`self._market_data_by_time`存储按时间分组后的市场数据
- 使用`self._all_pairs_data`存储所有交易对数据

#### 增量计算
- 通过比较当前日期和`self._last_calculated_date`判断是否需要重新计算
- 仅处理新数据点，跳过已计算的历史数据

#### 向量化操作
- 使用`pandas.DataFrame.rolling`进行滚动计算
- 使用`pandas.DataFrame.pct_change`计算百分比变化
- 使用`pandas.DataFrame.assign`批量添加新列

#### 日志增强
- 记录计算开始和结束时间
- 记录处理的数据点数量
- 记录每个交易对的关键指标

#### 错误处理
- 使用`pandas.DataFrame.columns.contains`检查列是否存在
- 使用`try-except`捕获数据处理异常

## 更新交易对杠杆信息

### 使用 binance_leverage_tiers.py 更新杠杆信息

执行以下命令来更新交易对的杠杆信息：

```bash
docker exec -it freqtrade python ft_userdata/binance_leverage_tiers.py
```

这个脚本会：
- 连接到 Binance API
- 获取所有交易对的最新杠杆层级信息
- 将信息保存到 `ft_userdata/binance_leverage_tiers.json` 文件中

#### 注意事项
- 确保 `binance_leverage_tiers.py` 文件中的 API 密钥配置正确
- 建议定期执行此更新以保持杠杆信息的准确性
- 更新后重启 Freqtrade 服务以使更改生效

## Freqtrade WebUI 快速指南

### 访问与登录
- WebUI 地址：http://localhost:8081
- API 地址：http://localhost:8080
- 默认用户名/密码：freqtrade / freqtrade（可在 config.json 中修改）

### 启动模式
- 回测模式：
  ```shell
  freqtrade webserver --config /freqtrade/ft_userdata/user_data/backtest.json --user-data-dir /freqtrade/ft_userdata/user_data
  ```
- 模拟交易模式：
  ```shell
  freqtrade trade --config /freqtrade/ft_userdata/user_data/1H_Up_Release.json --user-data-dir /freqtrade/ft_userdata/user_data --strategy All_1H_Up
  ```

### 配置要求
config.json 示例：
```json
"api_server": {
    "enabled": true,
    "listen_ip_address": "0.0.0.0",
    "listen_port": 8080,
    "verbosity": "error",
    "enable_openapi": true,
    "jwt_secret_key": "somethingrandom",
    "CORS_origins": ["http://localhost:8081"],
    "username": "freqtrade",
    "password": "freqtrade"
}
```

### 常见问题与修复
- "Dataprovider was not initialized with a pairlist provider" 错误：
  1. docker-compose.yml 使用 webserver 模式。
  2. 策略文件中 self.dp.current_whitelist() 建议加 try-except，self.logger 不存在时用 print 兜底。
  3. 重启容器：
     ```shell
     docker-compose down && docker-compose up -d
     ```
